import 'dotenv/config';
import { sql, eq, isNull, and, desc, count, like } from 'drizzle-orm';
import { drizzle } from "drizzle-orm/mysql2";
import mysql from "mysql2/promise";
import { knNews, knNewsClassify, knNewsContent, knNewsSeo } from "./schema";
import { alias } from 'drizzle-orm/mysql-core';
import { db } from './utils';

// 定义分类信息类型
export interface NewsClassifyInfo {
  id: string;
  classifyName: string | null;
  uniqueIdentification: string | null;
  // sorting: number | null;
  status: number | null;
  // newsCount: number | null;
  // createdAt: string | null;
  // updatedAt: string | null;
}

// 定义返回类型
export interface NewsWithDetails {
  id: string;
  title: string | null;
  shortTitle: string | null;
  introduction: string | null;
  picture: string | null;
  source: string | null;
  publishTime: string | null;
  viewCount: number | null;
  isPublish: number | null;
  cateId: number | null;
  newsClassifys: string | null;
  newsLabels: string | null;
  createdAt: string | null;
  updatedAt: string | null;
  seoInfo: {
    id: string | null;
    seoTitle: string | null;
    seoKeyword: string | null;
    seoDescription: string | null;
    // url: string | null;
    seoType: number | null;
  } | null;
  contentInfo: {
    id: string | null;
    content: string | null;
    createdAt: string | null;
    updatedAt: string | null;
  } | null;
  classifyInfo: NewsClassifyInfo[];
}

/**
 * 根据分类ID字符串获取分类详情信息
 * @param classifyIds 分类ID字符串，格式如 "1,2,3"
 * @returns 分类信息数组
 */
export async function getNewsClassifyDetails(classifyIds: string | null): Promise<NewsClassifyInfo[]> {
  if (!classifyIds || classifyIds.trim() === '') {
    return [];
  }

  try {
    // 将分类ID字符串分割成数组
    const idArray = classifyIds.split(',').map(id => id.trim()).filter(id => id !== '');

    if (idArray.length === 0) {
      return [];
    }

    // 查询分类信息
    const classifyResult = await db.select({
      id: knNewsClassify.id,
      classifyName: knNewsClassify.classifyName,
      uniqueIdentification: knNewsClassify.uniqueIdentification,
      // sorting: knNewsClassify.sorting,
      status: knNewsClassify.status,
      // newsCount: knNewsClassify.newsCount,
      // createdAt: knNewsClassify.createdAt,
      // updatedAt: knNewsClassify.updatedAt,
    })
    .from(knNewsClassify)
    .where(
      and(
        sql`${knNewsClassify.id} IN (${sql.join(idArray.map(id => sql`${id}`), sql`, `)})`,
        eq(knNewsClassify.deletedAt, 1) // 确保分类记录未被删除
      )
    );

    return classifyResult;
  } catch (error) {
    console.error('查询分类信息出错:', error);
    return [];
  }
}

/**
 * 根据新闻ID查询新闻详情，包括SEO信息、内容信息和分类信息
 * @param newsId 新闻ID
 * @returns 新闻详情信息，如果未找到则返回null
 */
export async function getNewsWithDetails(newsId: string): Promise<NewsWithDetails | null> {
  try {
    const result = await db.select({
      // 新闻主表字段
      id: knNews.id,
      title: knNews.title,
      shortTitle: knNews.shortTitle,
      introduction: knNews.introduction,
      picture: knNews.picture,
      source: knNews.source,
      publishTime: knNews.publishTime,
      viewCount: knNews.viewCount,
      isPublish: knNews.isPublish,
      cateId: knNews.cateId,
      newsClassifys: knNews.newsClassifys,
      newsLabels: knNews.newsLabels,
      createdAt: knNews.createdAt,
      updatedAt: knNews.updatedAt,

      // SEO 信息
      seoInfo: {
        id: knNewsSeo.id,
        seoTitle: knNewsSeo.seoTitle,
        seoKeyword: knNewsSeo.seoKeyword,
        seoDescription: knNewsSeo.seoDescription,
        // url: knNewsSeo.url,
        seoType: knNewsSeo.seoType,
      },

      // 内容信息
      contentInfo: {
        id: knNewsContent.id,
        content: knNewsContent.content,
        createdAt: knNewsContent.createdAt,
        updatedAt: knNewsContent.updatedAt,
      }
    })
    .from(knNews)
    // 左联接 SEO 表
    .leftJoin(
      knNewsSeo,
      and(
        eq(knNewsSeo.relationId, knNews.id),
        eq(knNewsSeo.deletedAt, 1) // 确保 SEO 记录未被删除
      )
    )
    // 左联接内容表
    .leftJoin(
      knNewsContent,
      and(
        eq(knNewsContent.newsId, knNews.id),
        eq(knNewsContent.deletedAt, 1) // 确保内容记录未被删除
      )
    )
    .where(
      and(
        eq(knNews.id, newsId), // 查询指定 ID 的新闻
        eq(knNews.deletedAt, 1) // 确保新闻记录未被删除
      )
    )
    .limit(1); // 限制返回一条记录

    if (result.length === 0) {
      return null;
    }

    const newsData = result[0];

    // 获取分类信息
    const classifyInfo = await getNewsClassifyDetails(newsData.newsClassifys);

    // 组合完整的新闻详情
    const newsWithDetails: NewsWithDetails = {
      ...newsData,
      classifyInfo
    };

    return newsWithDetails;
  } catch (error) {
    console.error('查询新闻详情出错:', error);
    throw error;
  }
}

/**
 * 打印新闻详情信息
 * @param news 新闻详情对象
 */
export function printNewsDetails(news: NewsWithDetails): void {
  console.log('\n=== 新闻基本信息 ===');
  console.log(`ID: ${news.id}`);
  console.log(`标题: ${news.title}`);
  console.log(`简短标题: ${news.shortTitle}`);
  console.log(`简介: ${news.introduction}`);
  console.log(`发布时间: ${news.publishTime}`);
  console.log(`浏览次数: ${news.viewCount}`);
  console.log(`分类ID: ${news.cateId}`);
  console.log(`新闻分类ID字符串: ${news.newsClassifys}`);

  console.log('\n=== 分类详情信息 ===');
  if (news.classifyInfo && news.classifyInfo.length > 0) {
    console.log(`分类数量: ${news.classifyInfo.length}`);
    news.classifyInfo.forEach((classify, index) => {
      console.log(`\n分类 ${index + 1}:`);
      console.log(`  - ID: ${classify.id}`);
      console.log(`  - 分类名称: ${classify.classifyName}`);
      console.log(`  - 唯一标识: ${classify.uniqueIdentification}`);
      // console.log(`  - 排序: ${classify.sorting}`);
      console.log(`  - 状态: ${classify.status}`);
      // console.log(`  - 新闻数量: ${classify.newsCount}`);
      // console.log(`  - 创建时间: ${classify.createdAt}`);
    });
  } else {
    console.log('该新闻暂无分类信息');
  }

  console.log('\n=== SEO 信息 ===');
  if (news.seoInfo && news.seoInfo.id) {
    console.log(`SEO ID: ${news.seoInfo.id}`);
    console.log(`SEO 标题: ${news.seoInfo.seoTitle}`);
    console.log(`SEO 关键词: ${news.seoInfo.seoKeyword}`);
    console.log(`SEO 描述: ${news.seoInfo.seoDescription}`);
    // console.log(`URL: ${news.seoInfo.url}`);
    console.log(`SEO 类型: ${news.seoInfo.seoType}`);
  } else {
    console.log('该新闻暂无 SEO 信息');
  }

  console.log('\n=== 内容信息 ===');
  if (news.contentInfo && news.contentInfo.id) {
    console.log(`内容 ID: ${news.contentInfo.id}`);
    console.log(`内容长度: ${news.contentInfo.content ? news.contentInfo.content.length : 0} 字符`);
    console.log(`内容创建时间: ${news.contentInfo.createdAt}`);
    console.log(`内容更新时间: ${news.contentInfo.updatedAt}`);
    // 只显示内容的前200个字符，避免输出过长
    if (news.contentInfo.content) {
      const contentPreview = news.contentInfo.content.length > 200
        ? news.contentInfo.content.substring(0, 200) + '...'
        : news.contentInfo.content;
      console.log(`内容预览: ${contentPreview}`);
    }
  } else {
    console.log('该新闻暂无内容信息');
  }
}

async function main() {
  try {
    const newsId = '1128113088472006656';
    // const newsId = '975911080450785280';
    console.log(`正在查询新闻 ID: ${newsId}`);

    // 使用封装的函数查询新闻详情
    const news = await getNewsWithDetails(newsId);

    if (!news) {
      console.log('未找到指定 ID 的新闻记录');
      return;
    }

    // 打印完整的查询结果（JSON格式）
    console.log('\n=== 完整查询结果 (JSON) ===');
    console.log(JSON.stringify(news, null, 2));

    // 打印格式化的新闻详情
    printNewsDetails(news);

  } catch (error) {
    console.error('查询出错:', error);
  }
}

main();