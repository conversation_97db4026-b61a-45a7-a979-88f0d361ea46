import 'dotenv/config';
import { sql, eq, isNull, and, desc, count, like } from 'drizzle-orm';
import { drizzle } from "drizzle-orm/mysql2";
import mysql from "mysql2/promise";
import { knNews, knNewsClassify, knNewsContent, knNewsSeo } from "./schema";
import { alias } from 'drizzle-orm/mysql-core';
import { db } from './utils';

async function main() {
  // const _knNewsContent = alias(knNewsContent,'contentMap')
  // const [news] = await db.execute(sql`SELECT * FROM kn_news WHERE id = 1095191088763461632`)
  // const news = await db.select().from('kn_news');
  // const findFirst = await db.query.knNews.findFirst()
  // const findFirst = await db.query.knNews.findMany()
  // console.log(findFirst)
  // const result = await db
  //   .select({
  //     // 新闻主表字段
  //     newsId: knNews.id,
  //     title: knNews.title,
  //     shortTitle: knNews.shortTitle,
  //     // 新闻内容
  //     content: knNewsContent.content,
  //     newsClassifys: knNews.newsClassifys,
  //     // 分类名称
  //     classifyName: knNewsClassify.classifyName
  //   })
  //   .from(knNews)
  //   // 关联新闻内容表
  //   .leftJoin(
  //     knNewsContent,
  //     eq(knNews.id, knNewsContent.newsId)
  //   )
  //   // 关联新闻分类表，这里假设newsClassifys存储的是分类ID的字符串（如"1,2,3"）
  //   .leftJoin(
  //     knNewsClassify,
  //     like(knNews.newsClassifys, `%${knNewsClassify.id}%`)
  //   ).where(
  //     and(
  //       // eq(knNews.id, '1095191088763461632'),
  //       eq(knNews.deletedAt, 1),
  //       // isNull(knNews.deletedAt)
  //     )
  //   )
  //   .orderBy(desc(knNewsContent.createdAt)).limit(2)
  // console.log(result)
  // return
  try {
    const news = await db.select({
      id: knNews.id,
      title: knNews.title,
      newsClassifys: knNews.newsClassifys,
      // classifyNames: sql`GROUP_CONCAT(${knNewsClassify.classifyName} SEPARATOR ', ')`.as('classifyNames'), 
      // contentId: knNewsContent.id,
      // contentCreatedAt: knNewsContent.createdAt,
      // content:knNewsContent.content,
      // count: count()
      // seoTitle: knNewsSeo.seoTitle,
      seo: {
        seoTitle: knNewsSeo.seoTitle,
        seoKeyword: knNewsSeo.seoKeyword,
        seoDescription: knNewsSeo.seoDescription,
      }
    })
      .from(knNews)
      // .leftJoin(knNewsContent, eq(knNews.id, knNewsContent.newsId))
      .leftJoin(knNewsSeo, eq(knNewsSeo.relationId, '1128113088472006656'))
      // .innerJoin(knNewsSeo, eq(knNews.id, knNewsSeo.relationId))
      // .innerJoin(knNewsContent, eq(knNews.id, knNewsContent.newsId))
      .where(
        // eq(knNews.id, '1128113088472006656'),
        and(
          eq(knNews.id, '1128113088472006656'),
        //   // eq(knNews.deletedAt, 1),
        //   // isNull(knNews.deletedAt)
        )
      )
      // .orderBy(desc(knNewsContent.createdAt))
      // .limit(1)
      // .catch((error) => {
      //   console.log("error：")
      //   console.log(error)
      // })
    console.log(news)

  } catch (error) {
    console.log(error)

  }

  //   const user: typeof usersTable.$inferInsert = {
  //     name: 'John',
  //     age: 30,
  //     email: '<EMAIL>',
  //   };
  //   await db.insert(usersTable).values(user);
  //   console.log('New user created!')
  //   const users = await db.select().from(usersTable);
  //   console.log('Getting all users from the database: ', users)
  //   /*
  //   const users: {
  //     id: number;
  //     name: string;
  //     age: number;
  //     email: string;
  //   }[]
  //   */
  //   await db
  //     .update(usersTable)
  //     .set({
  //       age: 31,
  //     })
  //     .where(eq(usersTable.email, user.email));
  //   console.log('User info updated!')
  //   await db.delete(usersTable).where(eq(usersTable.email, user.email));
  //   console.log('User deleted!')
}
main();