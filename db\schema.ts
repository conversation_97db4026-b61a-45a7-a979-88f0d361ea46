import { mysqlTable, mysqlSchema, AnyMySqlColumn, index, primaryKey, varchar, datetime, int, longtext, text } from "drizzle-orm/mysql-core"
import { sql } from "drizzle-orm"

export const knClassifyLabels = mysqlTable("kn_classify_labels", {
	id: varchar({ length: 36 }).notNull(),
	newsId: varchar("news_id", { length: 36 }),
	labelKeyId: varchar("label_key_id", { length: 36 }),
	labelNapeName: varchar("label_nape_name", { length: 36 }),
	labelValueId: varchar("label_value_id", { length: 36 }),
	province: varchar({ length: 36 }).default('-1'),
	city: varchar({ length: 36 }).default('-1'),
	area: varchar({ length: 36 }).default('-1'),
	street: varchar({ length: 36 }).default('-1'),
	level1: varchar("level_1", { length: 36 }),
	level2: varchar("level_2", { length: 36 }),
	level3: varchar("level_3", { length: 36 }),
	level4: varchar("level_4", { length: 36 }),
	createdAt: datetime("created_at", { mode: 'string'}).default(sql`(CURRENT_TIMESTAMP)`),
	updatedAt: datetime("updated_at", { mode: 'string'}).default(sql`(CURRENT_TIMESTAMP)`),
	deletedAt: int("deleted_at").default(1),
	createAdminId: varchar("create_admin_id", { length: 36 }),
	updateAdminId: varchar("update_admin_id", { length: 36 }),
},
(table) => [
	index("created_at").on(table.createdAt),
	primaryKey({ columns: [table.id], name: "kn_classify_labels_id"}),
]);

export const knNews = mysqlTable("kn_news", {
	id: varchar({ length: 36 }).notNull(),
	shortTitle: varchar("short_title", { length: 64 }),
	title: varchar({ length: 255 }),
	introduction: varchar({ length: 500 }),
	picture: varchar({ length: 255 }),
	isHotspot: int("is_hotspot").default(0),
	isRecommend: int("is_recommend"),
	ipCount: int("ip_count").default(0),
	viewCount: int("view_count").default(0),
	uvCount: int("uv_count").default(0),
	isTop: int("is_top").default(0),
	sumViewCount: int("sum_view_count").default(0),
	randomViewCount: int("random_view_count"),
	source: varchar({ length: 128 }),
	isAnswers: varchar("is_answers", { length: 255 }),
	timelinessEnd: datetime("timeliness_end", { mode: 'string'}),
	isUpload: int("is_upload"),
	publishSetting: int("publish_setting"),
	isPublish: int("is_publish").default(0),
	publishTime: datetime("publish_time", { mode: 'string'}),
	addedTime: datetime("added_time", { mode: 'string'}),
	undercarriageTime: datetime("undercarriage_time", { mode: 'string'}),
	sorting: int(),
	isIncluded: int("is_included"),
	baiduPushM: int("baidu_push_m").default(0),
	baiduPushPc: int("baidu_push_pc").default(0),
	cateId: int("cate_id").default(0),
	studyLevel2: int("study_level2").default(0),
	studyLevel3: int("study_level3").default(0),
	studyLevel4: int("study_level4").default(0),
	// newsClassifys关联表knNewsClassify，是多个分类id，用逗号分隔
	newsClassifys: varchar("news_classifys", { length: 1000 }),
	productIds: varchar("product_ids", { length: 1000 }),
	newsLabels: varchar("news_labels", { length: 1000 }),
	isListConceal: int("is_list_conceal").default(0),
	province: int().default(-1),
	city: int().default(-1),
	area: int().default(-1),
	street: int().default(-1),
	subjectId: int("subject_id").default(-1),
	classType: int("class_type").default(-1),
	examFormatId: int("exam_format_id").default(-1),
	academicSectionId: int("academic_section_id").default(-1),
	directionId: int("direction_id").default(-1),
	orgId: varchar("org_id", { length: 255 }),
	version: int().default(1),
	createdAt: datetime("created_at", { mode: 'string'}).default(sql`(CURRENT_TIMESTAMP)`),
	createAdminId: varchar("create_admin_id", { length: 36 }),
	updateAdminId: varchar("update_admin_id", { length: 36 }),
	updatedAt: datetime("updated_at", { mode: 'string'}).default(sql`(CURRENT_TIMESTAMP)`),
	deletedAt: int("deleted_at").default(1),
	updateEs: int("update_es").default(0),
	updateEs1: int("update_es1").default(0),
},
(table) => [
	index("cate_id_idx").on(table.cateId),
	index("cate_publish_idx").on(table.cateId, table.publishTime),
	index("created_at").on(table.createdAt),
	index("idx_cate_id").on(table.cateId),
	index("is_publish_idx").on(table.isPublish),
	index("ORDER_INDEX_1").on(table.updatedAt),
	index("publish_time").on(table.publishTime),
	index("QUERY_INDEX_1").on(table.productIds),
	index("timeliness_end").on(table.timelinessEnd),
	primaryKey({ columns: [table.id], name: "kn_news_id"}),
]);

export const knNewsClassify = mysqlTable("kn_news_classify", {
	id: varchar({ length: 36 }).notNull(),
	productIds: varchar("product_ids", { length: 1000 }),
	cateIds: varchar("cate_ids", { length: 1000 }),
	classifyName: varchar("classify_name", { length: 64 }),
	sorting: int(),
	uniqueIdentification: varchar("unique_identification", { length: 255 }),
	isStudentQuestions: int("is_student_questions"),
	status: int(),
	newsCount: int("news_count").default(0).notNull(),
	orgId: varchar("org_id", { length: 1000 }),
	createdAt: datetime("created_at", { mode: 'string'}).default(sql`(CURRENT_TIMESTAMP)`),
	updatedAt: datetime("updated_at", { mode: 'string'}).default(sql`(CURRENT_TIMESTAMP)`),
	deletedAt: int("deleted_at").default(1),
	createAdminId: varchar("create_admin_id", { length: 36 }),
	updateAdminId: varchar("update_admin_id", { length: 36 }),
},
(table) => [
	index("created_at").on(table.createdAt),
	index("sorting").on(table.sorting),
	primaryKey({ columns: [table.id], name: "kn_news_classify_id"}),
]);

export const knNewsContent = mysqlTable("kn_news_content", {
	id: varchar({ length: 36 }).notNull(),
	newsId: varchar("news_id", { length: 36 }),
	content: longtext(),
	createdAt: datetime("created_at", { mode: 'string'}).default(sql`(CURRENT_TIMESTAMP)`),
	updatedAt: datetime("updated_at", { mode: 'string'}).default(sql`(CURRENT_TIMESTAMP)`),
	deletedAt: int("deleted_at").default(1),
},
(table) => [
	index("QUERY_NEWSID").on(table.newsId),
	primaryKey({ columns: [table.id], name: "kn_news_content_id"}),
]);

export const knNewsLabel = mysqlTable("kn_news_label", {
	id: varchar({ length: 36 }).notNull(),
	productIds: varchar("product_ids", { length: 1000 }),
	cateIds: varchar("cate_ids", { length: 1000 }),
	labelName: varchar("label_name", { length: 32 }),
	sorting: int(),
	uniqueIdentification: varchar("unique_identification", { length: 255 }),
	status: int(),
	newsCount: int("news_count").default(0).notNull(),
	orgId: varchar("org_id", { length: 1000 }),
	createdAt: datetime("created_at", { mode: 'string'}).default(sql`(CURRENT_TIMESTAMP)`),
	updatedAt: datetime("updated_at", { mode: 'string'}).default(sql`(CURRENT_TIMESTAMP)`),
	deletedAt: int("deleted_at").default(1),
	createAdminId: varchar("create_admin_id", { length: 36 }),
	updateAdminId: varchar("update_admin_id", { length: 36 }),
},
(table) => [
	primaryKey({ columns: [table.id], name: "kn_news_label_id"}),
]);

export const knNewsSeo = mysqlTable("kn_news_seo", {
	id: varchar({ length: 36 }).notNull(),
	relationId: varchar("relation_id", { length: 36 }),
	productId: int("product_id"),
	cateId: int("cate_id"),
	// 类型 1标签 2分类 3新闻  4热词
	seoType: int("seo_type"),
	seoTitle: varchar("seo_title", { length: 1024 }),
	seoKeyword: text("seo_keyword"),
	seoDescription: text("seo_description"),
	url: varchar({ length: 500 }),
	sorting: int().default(0).notNull(),
	createdAt: datetime("created_at", { mode: 'string'}).default(sql`(CURRENT_TIMESTAMP)`),
	updatedAt: datetime("updated_at", { mode: 'string'}).default(sql`(CURRENT_TIMESTAMP)`),
	deletedAt: int("deleted_at").default(1),
	createAdminId: varchar("create_admin_id", { length: 36 }),
	updateAdminId: varchar("update_admin_id", { length: 36 }),
},
(table) => [
	index("QUERY_SEO").on(table.relationId, table.seoType),
	primaryKey({ columns: [table.id], name: "kn_news_seo_id"}),
]);
