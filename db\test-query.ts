import 'dotenv/config';
import { eq, and } from 'drizzle-orm';
import { knNews, knNewsContent, knNewsSeo } from "./schema";
import { db } from './utils';

async function testQuery() {
  try {
    console.log('开始查询新闻 ID: 1128113088472006656');
    
    const result = await db.select({
      // 新闻主表字段
      id: knNews.id,
      title: knNews.title,
      shortTitle: knNews.shortTitle,
      introduction: knNews.introduction,
      picture: knNews.picture,
      source: knNews.source,
      publishTime: knNews.publishTime,
      viewCount: knNews.viewCount,
      isPublish: knNews.isPublish,
      cateId: knNews.cateId,
      newsClassifys: knNews.newsClassifys,
      newsLabels: knNews.newsLabels,
      createdAt: knNews.createdAt,
      updatedAt: knNews.updatedAt,
      
      // SEO 信息
      seoInfo: {
        id: knNewsSeo.id,
        seoTitle: knNewsSeo.seoTitle,
        seoKeyword: knNewsSeo.seoKeyword,
        seoDescription: knNewsSeo.seoDescription,
        url: knNewsSeo.url,
        seoType: knNewsSeo.seoType,
      },
      
      // 内容信息
      contentInfo: {
        id: knNewsContent.id,
        content: knNewsContent.content,
        createdAt: knNewsContent.createdAt,
        updatedAt: knNewsContent.updatedAt,
      }
    })
    .from(knNews)
    // 左联接 SEO 表
    .leftJoin(
      knNewsSeo, 
      and(
        eq(knNewsSeo.relationId, knNews.id),
        eq(knNewsSeo.deletedAt, 1) // 确保 SEO 记录未被删除
      )
    )
    // 左联接内容表
    .leftJoin(
      knNewsContent,
      and(
        eq(knNewsContent.newsId, knNews.id),
        eq(knNewsContent.deletedAt, 1) // 确保内容记录未被删除
      )
    )
    .where(
      and(
        eq(knNews.id, '1128113088472006656'), // 查询指定 ID 的新闻
        eq(knNews.deletedAt, 1) // 确保新闻记录未被删除
      )
    )
    .limit(1); // 限制返回一条记录

    console.log('查询完成，结果数量:', result.length);
    
    if (result.length > 0) {
      const news = result[0];
      console.log('\n=== 查询结果 ===');
      console.log('新闻ID:', news.id);
      console.log('标题:', news.title);
      console.log('简介:', news.introduction);
      console.log('发布时间:', news.publishTime);
      console.log('浏览次数:', news.viewCount);
      
      if (news.seoInfo && news.seoInfo.id) {
        console.log('\nSEO信息:');
        console.log('- SEO标题:', news.seoInfo.seoTitle);
        console.log('- SEO关键词:', news.seoInfo.seoKeyword);
        console.log('- SEO描述:', news.seoInfo.seoDescription);
      } else {
        console.log('\n无SEO信息');
      }
      
      if (news.contentInfo && news.contentInfo.id) {
        console.log('\n内容信息:');
        console.log('- 内容ID:', news.contentInfo.id);
        console.log('- 内容长度:', news.contentInfo.content ? news.contentInfo.content.length : 0);
        console.log('- 内容预览:', news.contentInfo.content ? news.contentInfo.content.substring(0, 100) + '...' : '无内容');
      } else {
        console.log('\n无内容信息');
      }
    } else {
      console.log('未找到指定ID的新闻');
    }
    
  } catch (error) {
    console.error('查询出错:', error);
  } finally {
    process.exit(0);
  }
}

testQuery();
